import { memo, useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import ApprovalCard from './ApprovalCard';

const ITEM_HEIGHT = 320;

const ApprovalList = memo(({
  items,
  hasNextPage,
  loadNextPage,
  selectedCards,
  onCardSelect,
  setShowSidebar,
  setSidebarData,
  setSidebarDataType,

  setShowEmailModal,
  setDataForMail,
}) => {
  const itemCount = useMemo(() => {
    return hasNextPage ? items.length + 1 : items.length;
  }, [items.length, hasNextPage]);

  const isItemLoaded = useCallback((index) => {
    return !!items[index];
  }, [items]);

  const Row = memo(({ index, style }) => {
    const item = items[index];
    if (!item) {
      return (
        <div style={style} className="flex items-center justify-center px-4 py-2">
          <div className="bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200 rounded-2xl w-full h-72 flex items-center justify-center shadow-sm">
            <div className="flex flex-col items-center gap-4">
              <div className="relative">
                <div className="animate-spin rounded-full h-10 w-10 border-4 border-blue-200"></div>
                <div className="animate-spin rounded-full h-10 w-10 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"></div>
              </div>
              <div className="text-center">
                <span className="text-sm font-medium text-slate-700">Loading more approvals...</span>
                <div className="text-xs text-slate-500 mt-1">Please wait</div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    const selectedTab = item._category || 'unknown';

    return (
      <div style={style} className="px-4 py-2">
        <ApprovalCard
          key={item._id}
          selectedTab={selectedTab}
          data={item}
          setShowSidebar={setShowSidebar}
          setSidebarData={setSidebarData}
          setSidebarDataType={setSidebarDataType}

          setShowEmailModal={setShowEmailModal}
          setDataForMail={setDataForMail}
          isSelected={selectedCards.has(item._id)}
          onSelect={onCardSelect}
        />
      </div>
    );
  });

  Row.displayName = 'ApprovalRow';

  return (
    <InfiniteLoader
      isItemLoaded={isItemLoaded}
      itemCount={itemCount}
      loadMoreItems={loadNextPage}
    >
      {({ onItemsRendered, ref }) => (
        <List
          ref={ref}
          height={1000}
          itemCount={itemCount}
          itemSize={ITEM_HEIGHT}
          onItemsRendered={onItemsRendered}
          overscanCount={5}
        >
          {Row}
        </List>
      )}
    </InfiniteLoader>
  );
});

ApprovalList.displayName = 'ApprovalList';

export default ApprovalList;

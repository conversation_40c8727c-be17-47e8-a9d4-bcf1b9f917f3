import { memo, useCallback, useMemo } from 'react';
import {
  IoCalendarOutline,
  IoCheckmarkCircle,
  IoCloseCircle,
  IoEyeOutline,
  IoMailOutline,
  IoPrintOutline,
} from 'react-icons/io5';

import { toast } from 'react-toastify';
import {
  getLocalDateTime,
  handlePdf,
  unCamelCaseString,
} from '../../helperFunction';
import { useUpdateDepRowStatusMutation } from '../../slices/departmentRowApiSlice';
import { useEditIndentMutation } from '../../slices/indentApiSlice';
import { useLazyGetPdfQuery } from '../../slices/pdfApiSlice';
import { useUpdatePurchaseOrderStatusMutation } from '../../slices/purchaseOrderApiSlice';
import { useUpdateQuotationsMutation } from '../../slices/quotationApiSlice';
import { useUpdateSalesOrderMutation } from '../../slices/salesOrderSlices';

const FieldRenderer = memo(({ selectedTab, data }) => {
  const fieldConfig = useMemo(() => ({
    purchaseOrders: [
      { label: 'PO Number', value: data?.poID || 'N/A', important: true },
      { label: 'Vendor', value: data?.vendor?.name || 'N/A' },
      { label: 'Total Amount', value: `₹${data?.total || 0}`, important: true },
    ],
    purchaseIndents: [
      { label: 'Indent Number', value: data?.indent_no || 'N/A', important: true },
      { label: 'Status', value: data?.status || 'N/A' },
      { label: 'Item', value: data?.product_name || 'N/A' },
    ],
    salesQuotations: [
      { label: 'Quote Number', value: data?.quoteID || 'N/A', important: true },
      { label: 'Customer', value: data?.vendorDetails?.name || 'N/A' },
      { label: 'Valid Until', value: data?.date?.expiryDate ? getLocalDateTime(data.date.expiryDate) : 'N/A' },
    ],
    salesOrders: [
      { label: 'Order Number', value: data?.salesOrderID || 'N/A', important: true },
      { label: 'Customer', value: data?.customer || 'N/A' },
      { label: 'Delivery Date', value: data?.deliveryDate ? getLocalDateTime(data.deliveryDate) : 'N/A' },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }), [data, selectedTab]);

  const fields = fieldConfig[selectedTab] || [
    { label: 'ID', value: data?._id || 'N/A', important: true },
    { label: 'Status', value: data?.status || 'Pending' },
    { label: 'Created', value: getLocalDateTime(data?.createdAt) },
  ];

  return (
    <div className="space-y-3">
      {fields.map((field, index) => (
        <div key={index} className="flex flex-col space-y-1">
          <span className="text-slate-500 text-xs font-medium uppercase tracking-wide">
            {field.label}
          </span>
          <span
            className={`text-sm truncate ${field.important
              ? 'font-bold text-slate-900 bg-white px-2 py-1 rounded-md border border-slate-200'
              : 'font-medium text-slate-700'
              }`}
          >
            {field.value}
          </span>
        </div>
      ))}
    </div>
  );
});

FieldRenderer.displayName = 'FieldRenderer';

const getStatusColor = (selectedTab) => {
  const colors = {
    purchaseOrders: 'bg-blue-100 text-blue-800 border-blue-200',
    purchaseIndents: 'bg-purple-100 text-purple-800 border-purple-200',
    salesQuotations: 'bg-emerald-100 text-emerald-800 border-emerald-200',
    salesOrders: 'bg-orange-100 text-orange-800 border-orange-200',
  };
  return colors[selectedTab] || 'bg-slate-100 text-slate-800 border-slate-200';
};


const ApprovalCard = memo(({
  selectedTab,
  data,
  setShowSidebar,
  setSidebarData,
  setSidebarDataType,
  setShowEmailModal,
  setDataForMail,
  isSelected = false,
  onSelect,
}) => {
  const [updatePurchaseOrderStatus, { isLoading: isLoading1 }] = useUpdatePurchaseOrderStatusMutation();
  const [editIndentdatas, { isLoading: isLoading2 }] = useEditIndentMutation();
  const [updateQuotations, { isLoading: isLoading3 }] = useUpdateQuotationsMutation();
  const [updateSalesOrder, { isLoading: isLoading4 }] = useUpdateSalesOrderMutation();
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const [updateDepRowStatus, { isLoading: isLoading5 }] = useUpdateDepRowStatusMutation();

  const isLoading = isLoading1 || isLoading2 || isLoading3 || isLoading4 || isLoading5 || isFetchingPdf;

  const { cardId, cardName } = useMemo(() => {
    const idMap = {
      purchaseOrders: data?.poID || data?._id,
      purchaseIndents: data?.indent_no || data?._id,
      salesQuotations: data?.quoteID || data?._id,
      salesOrders: data?.salesOrderID || data?._id,
    };

    const nameMap = {
      purchaseOrders: 'Purchase Order',
      purchaseIndents: 'Purchase Indent',
      salesQuotations: 'Sales Quotation',
      salesOrders: 'Sales Order',
    };

    return {
      cardId: idMap[selectedTab] || data?.customTaskId || data?._id,
      cardName: nameMap[selectedTab] || unCamelCaseString(selectedTab)
    };
  }, [selectedTab, data]);

  // Memoized action handler
  const handleAction = useCallback(async (id, isApproved, fullData = null) => {
    try {
      if (selectedTab === 'purchaseOrders') {
        await updatePurchaseOrderStatus({
          poStatus: isApproved ? 'Approved' : 'Rejected',
          id,
        }).unwrap();
      } else if (selectedTab === 'purchaseIndents') {
        await editIndentdatas({
          editdata: { status: isApproved ? 'approved' : 'rejected' },
          id,
        });
      } else if (selectedTab === 'salesQuotations') {
        await updateQuotations({
          data: {
            ...fullData,
            quoteStatus: isApproved ? 'Approved' : 'Rejected',
          },
          id,
        }).unwrap();
      } else if (selectedTab === 'salesOrders') {
        await updateSalesOrder({
          data: {
            salesOrderStatus: isApproved ? 'approved' : 'rejected',
          },
          id,
        }).unwrap();
      } else {
        await updateDepRowStatus({
          data: {
            status: isApproved ? 'approved' : 'rejected',
          },
          id,
        }).unwrap();
      }

      toast.success(
        `Successfully ${isApproved ? 'approved' : 'rejected'} ${cardName}`,
        { toastId: `${id}-${isApproved ? 'approve' : 'reject'}` }
      );
    } catch (error) {
      toast.error(
        `Failed to ${isApproved ? 'approve' : 'reject'} ${cardName}. Please try again.`,
        { toastId: `${id}-error` }
      );
    }
  }, [selectedTab, cardName, updatePurchaseOrderStatus, editIndentdatas, updateQuotations, updateSalesOrder, updateDepRowStatus]);

  // Memoized checkbox handler
  const handleCheckboxChange = useCallback((e) => {
    onSelect?.(data._id, e.target.checked);
  }, [data._id, onSelect]);

  // Memoized view handler
  const handleView = useCallback(() => {
    setSidebarData(data);
    setSidebarDataType(selectedTab);
    setShowSidebar(true);
  }, [data, selectedTab, setSidebarData, setSidebarDataType, setShowSidebar]);

  // Memoized print handler
  const handlePrint = useCallback(async () => {
    try {
      const pdfType = {
        purchaseOrders: 'purchaseOrder',
        purchaseIndents: 'indent',
        salesQuotations: 'quotation',
        salesOrders: 'salesOrder',
      }[selectedTab] || selectedTab;

      await handlePdf(getPdf, data._id, pdfType);
    } catch (error) {
      toast.error('Failed to generate PDF');
    }
  }, [selectedTab, data._id, getPdf]);

  // Memoized email handler
  const handleEmail = useCallback(() => {
    setDataForMail(data);
    setShowEmailModal(true);
  }, [data, setDataForMail, setShowEmailModal]);

  return (
    <div className={`group relative bg-white border rounded-2xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-[1.01] ${isSelected
      ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg ring-2 ring-blue-200'
      : 'border-slate-200 hover:border-slate-300 shadow-sm'
      }`}>

      {/* Header */}
      <div className="p-5 pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            <div className="relative">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={handleCheckboxChange}
                className="w-5 h-5 text-blue-600 bg-white border-2 border-slate-300 rounded-md focus:ring-blue-500 focus:ring-2 transition-all duration-200"
              />
              {isSelected && (
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <IoCheckmarkCircle className="w-5 h-5 text-blue-600" />
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-2">
                <h3 className="text-lg font-bold text-slate-900 truncate">
                  {cardId}
                </h3>
                <span
                  className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(selectedTab)}`}
                >
                  {cardName}
                </span>
              </div>
              <div className="flex items-center gap-2 text-slate-500">
                <IoCalendarOutline className="w-4 h-4 flex-shrink-0" />
                <span className="text-sm font-medium">
                  {getLocalDateTime(data?.createdAt)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-5 pb-5 flex-1 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-full">
          {/* Details */}
          <div className="lg:col-span-2">
            <div className="bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl p-4 h-full overflow-y-auto border border-slate-200">
              <FieldRenderer selectedTab={selectedTab} data={data} />
            </div>
          </div>

          {/* Actions */}
          <div className="lg:col-span-1">
            <div className="space-y-3 h-full flex flex-col">
              {/* Primary Actions */}
              <div className="space-y-2">
                <button
                  disabled={isLoading}
                  onClick={() => handleAction(data?._id, true, data)}
                  className="w-full inline-flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 disabled:from-emerald-300 disabled:to-emerald-400 text-white text-sm font-semibold rounded-xl transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 disabled:transform-none"
                >
                  <IoCheckmarkCircle className="w-4 h-4" />
                  {isLoading ? 'Processing...' : 'Approve'}
                </button>
                <button
                  disabled={isLoading}
                  onClick={() => handleAction(data?._id, false)}
                  className="w-full inline-flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 disabled:from-red-300 disabled:to-red-400 text-white text-sm font-semibold rounded-xl transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 disabled:transform-none"
                >
                  <IoCloseCircle className="w-4 h-4" />
                  {isLoading ? 'Processing...' : 'Reject'}
                </button>
              </div>

              {/* Secondary Actions */}
              <div className="space-y-2 mt-auto">
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={handleView}
                    className="inline-flex items-center justify-center gap-1 px-3 py-2 bg-white hover:bg-slate-50 text-slate-700 text-xs font-medium rounded-lg border border-slate-200 hover:border-slate-300 transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <IoEyeOutline className="w-4 h-4" />
                    View
                  </button>
                  <button
                    onClick={handlePrint}
                    disabled={isFetchingPdf}
                    className="inline-flex items-center justify-center gap-1 px-3 py-2 bg-white hover:bg-slate-50 text-slate-700 text-xs font-medium rounded-lg border border-slate-200 hover:border-slate-300 transition-all duration-200 shadow-sm hover:shadow disabled:opacity-50"
                  >
                    <IoPrintOutline className="w-4 h-4" />
                    {isFetchingPdf ? 'PDF...' : 'Print'}
                  </button>
                </div>

                <button
                  onClick={handleEmail}
                  className="w-full inline-flex items-center justify-center gap-2 px-3 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-xs font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <IoMailOutline className="w-4 h-4" />
                  Send Email
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

ApprovalCard.displayName = 'ApprovalCard';

export default ApprovalCard;
